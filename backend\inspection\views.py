from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>, BrowsableAP<PERSON><PERSON><PERSON>
from rest_framework.views import APIView
from django.conf import settings
from django.http import FileResponse
import os
from io import BytesIO
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, Flowable
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.graphics.shapes import Drawing, Rect, Line
from .models import Inspection, UserFile, CompanyLogo, Company
from .serializers import InspectionSerializer, UserFileSerializer, CompanySerializer, CompanyLogoSerializer

def simple_checkbox(checked):
    """Return a simple checkmark symbol for checked items, blank for unchecked"""
    return '✓' if checked else ''

class UserFileViewSet(viewsets.ModelViewSet):
    serializer_class = UserFileSerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        folder_id = self.request.query_params.get('folder_id')
        queryset = UserFile.objects.filter(user=user)
        if folder_id:
            queryset = queryset.filter(folder_id=folder_id)
        return queryset.order_by('-upload_date')

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['get'], url_path='content')
    def content(self, request, pk=None):
        """
        Stream the actual file content via API so we avoid /media being handled by the frontend web server.
        Only the owner can access due to get_queryset filtering by user.
        """
        try:
            user_file = self.get_object()
            if not user_file.file:
                return Response({'error': 'File not found'}, status=404)

            import os
            import mimetypes
            file_path = user_file.file.path
            if not os.path.exists(file_path):
                return Response({'error': 'File missing on server'}, status=404)

            content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            file_handle = open(file_path, 'rb')
            resp = FileResponse(file_handle, content_type=content_type)
            # Inline display for previews
            resp['Content-Disposition'] = f"inline; filename=\"{user_file.name}\""
            resp['Content-Length'] = os.path.getsize(file_path)
            resp['Accept-Ranges'] = 'bytes'
            return resp
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.user != request.user:
            return Response({'detail': 'Not authorized to delete this file.'}, status=status.HTTP_403_FORBIDDEN)
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)




class CompanyViewSet(viewsets.ModelViewSet):
    serializer_class = CompanySerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Company.objects.filter(user=self.request.user).order_by('name')

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.user != request.user:
            return Response({'detail': 'Not authorized to delete this company.'}, status=status.HTTP_403_FORBIDDEN)
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)


class CheckBox(Flowable):
    """A custom checkbox that can be checked or unchecked"""

    def __init__(self, checked=False, size=10):
        Flowable.__init__(self)
        self.checked = checked
        self.size = size
        self.width = size
        self.height = size

    def draw(self):
        # Draw the checkbox border
        self.canv.rect(0, 0, self.size, self.size, stroke=1, fill=0)

        # If checked, draw an X inside the box
        if self.checked:
            # Draw an X mark
            self.canv.line(2, 2, self.size-2, self.size-2)
            self.canv.line(2, self.size-2, self.size-2, 2)

class PDFRenderer(BrowsableAPIRenderer):
    media_type = 'application/pdf'
    format = 'pdf'
    charset = None
    render_style = 'binary'

    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data

class InspectionViewSet(viewsets.ModelViewSet):
    serializer_class = InspectionSerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return only inspections created by the authenticated user"""
        return Inspection.objects.filter(created_by=self.request.user)

    def perform_create(self, serializer):
        """Automatically assign the authenticated user as the creator"""
        print(f"DEBUG: Creating inspection for authenticated user: {self.request.user.username}")
        serializer.save(created_by=self.request.user)

    def get_serializer(self, *args, **kwargs):
        """Override to handle JSON field parsing for FormData requests"""
        print(f"DEBUG: get_serializer called with args: {args}, kwargs keys: {list(kwargs.keys())}")

        if 'data' in kwargs and hasattr(self.request, 'content_type') and 'multipart/form-data' in self.request.content_type:
            print(f"DEBUG: get_serializer - Processing FormData request")
            data = kwargs['data'].copy()

            # Handle JSON fields that come as strings in FormData
            json_fields = ['points', 'photos_anomalies']
            for field in json_fields:
                if field in data and isinstance(data[field], str):
                    try:
                        import json
                        data[field] = json.loads(data[field])
                        print(f"DEBUG: get_serializer - Parsed JSON for field {field}")
                    except (json.JSONDecodeError, TypeError) as e:
                        print(f"DEBUG: get_serializer - Failed to parse JSON for field {field}: {e}")
                        # Set default values for failed JSON parsing
                        if field == 'points':
                            data[field] = {}
                        elif field == 'photos_anomalies':
                            data[field] = []
                elif field not in data:
                    # Field not present, set defaults
                    if field == 'points':
                        data[field] = {}
                    elif field == 'photos_anomalies':
                        data[field] = []

            kwargs['data'] = data
        else:
            print(f"DEBUG: get_serializer - Not a FormData request or no data")

        return super().get_serializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        print("DEBUG: *** CREATE METHOD CALLED - NEW VERSION ***")
        try:
            data = request.data.copy()

            # Handle JSON fields that come as strings in FormData
            json_fields = ['points', 'photos_anomalies']
            for field in json_fields:
                if field in data:
                    field_value = data[field]
                    print(f"DEBUG: Processing field {field}, type: {type(field_value)}, value: {repr(field_value)[:200]}")

                    if isinstance(field_value, str):
                        try:
                            import json
                            parsed_value = json.loads(field_value)
                            data[field] = parsed_value
                            print(f"DEBUG: Successfully parsed JSON for field {field}")
                        except (json.JSONDecodeError, TypeError) as e:
                            print(f"DEBUG: Failed to parse JSON for field {field}: {repr(field_value)[:100]}, error: {e}")
                            if field == 'points':
                                data[field] = {}
                            elif field == 'photos_anomalies':
                                data[field] = []
                    elif field_value is None or field_value == '' or field_value == 'null':
                        if field == 'points':
                            data[field] = {}
                        elif field == 'photos_anomalies':
                            data[field] = []
                else:
                    if field == 'points':
                        data[field] = {}
                    elif field == 'photos_anomalies':
                        data[field] = []

            # Handle multiple anomaly photos
            photos_anomalies = []
            photos_count = data.get('photos_anomalies_count', 0)
            try:
                photos_count = int(photos_count) if photos_count else 0
                for i in range(photos_count):
                    photo_key = f'photos_anomalies_{i}'
                    if photo_key in request.FILES:
                        photo_file = request.FILES[photo_key]
                        import os
                        from django.conf import settings
                        from django.core.files.storage import default_storage
                        import uuid
                        file_extension = os.path.splitext(photo_file.name)[1]
                        unique_filename = f"anomaly_photos/{uuid.uuid4()}{file_extension}"
                        anomaly_dir = os.path.join(settings.MEDIA_ROOT, 'anomaly_photos')
                        os.makedirs(anomaly_dir, exist_ok=True)
                        file_path = default_storage.save(unique_filename, photo_file)
                        full_path = os.path.join(settings.MEDIA_ROOT, file_path)
                        photos_anomalies.append(full_path)
            except (ValueError, TypeError):
                pass

            if photos_anomalies:
                data['photos_anomalies'] = photos_anomalies
            elif 'photos_anomalies' not in data or not data['photos_anomalies']:
                data['photos_anomalies'] = []

            # Check if fiche number should be generated automatically or use manual input
            fiche_generation_method = data.get('fiche_generation_method', 'manual')
            current_fiche_num = data.get('fiche_num', '')

            print(f"DEBUG: Received fiche_generation_method: {fiche_generation_method}")
            print(f"DEBUG: Received fiche_num: {current_fiche_num}")

            # If fiche_num starts with "COPY_", always treat as manual (even if method is automatic)
            # This handles edit mode where frontend generates COPY_ format for automatic mode
            if current_fiche_num and current_fiche_num.startswith('COPY_'):
                print(f"DEBUG: COPY format detected, using manual mode: {current_fiche_num}")
                # Don't override the COPY format - keep it as is
            elif fiche_generation_method == 'automatic':
                # Generate sequential fiche_num per user/project/date
                from django.db.models import Max
                user = request.user
                project = data.get('projet')
                date = data.get('date')

                # Query existing max sequence number for this user/project/date
                max_fiche_num = None
                if user and project and date:
                    # Filter inspections for user, project, and date
                    inspections = Inspection.objects.filter(created_by=user, projet=project, date=date)
                    # Extract max sequence number from fiche_num by parsing the prefix before first '/'
                    max_seq = 0
                    for insp in inspections:
                        fiche_num = insp.fiche_num
                        if fiche_num:
                            seq_part = fiche_num.split('/')[0]
                            try:
                                seq_num = int(seq_part)
                                if seq_num > max_seq:
                                    max_seq = seq_num
                            except ValueError:
                                continue
                    max_fiche_num = max_seq

                next_seq_num = (max_fiche_num or 0) + 1

                # Format fiche_num as "sequenceNumber/ProjectName/Date"
                formatted_fiche_num = f"{str(next_seq_num).zfill(3)}/{project}/{date}"
                data['fiche_num'] = formatted_fiche_num
                print(f"DEBUG: Auto-generated sequential fiche_num: {formatted_fiche_num}")
            else:
                # Use manual fiche number provided by frontend
                manual_fiche_num = data.get('fiche_num', '')
                print(f"DEBUG: Using manual fiche_num: {manual_fiche_num}")

            if request.user.is_authenticated:
                data['created_by'] = request.user.id

            print(f"DEBUG: Final fiche_num: {data.get('fiche_num', 'Not set')}")

            # Validate and fix points data structure for F, G, C fields
            points_data = data.get('points', {})
            if isinstance(points_data, dict):
                print(f"DEBUG: Validating points data with {len(points_data)} points")
                for point_key, point_value in points_data.items():
                    if isinstance(point_value, dict):
                        # Ensure f, g, c fields exist with default values
                        if 'f' not in point_value:
                            point_value['f'] = ''
                        if 'g' not in point_value:
                            point_value['g'] = ''
                        if 'c' not in point_value:
                            point_value['c'] = ''
                print(f"DEBUG: Points validation completed")
            else:
                print(f"DEBUG: Points data is not a dict: {type(points_data)}")

            serializer = self.serializer_class(data=data)
            if not serializer.is_valid():
                print(f"DEBUG: Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=400)

            inspection = serializer.save(created_by=request.user)
            print(f"DEBUG: Inspection created with authenticated user: {request.user.username}, ID: {inspection.id}")
            print("DEBUG: Inspection save completed")
            headers = self.get_success_headers(serializer.data)
            return Response(serializer.data, status=201, headers=headers)

        except Exception as e:
            print(f"DEBUG: Create method error: {str(e)}")
            import traceback
            print(f"DEBUG: Full traceback: {traceback.format_exc()}")

            # Return more detailed error information
            error_details = {
                'error': str(e),
                'error_type': type(e).__name__,
                'traceback': traceback.format_exc()
            }
            print(f"DEBUG: Returning error response: {error_details}")
            return Response(error_details, status=500)

    @action(detail=False, methods=['get', 'post'])
    def count(self, request):
        """Get count of inspections for authenticated user or clear data"""

        # Handle clear data request via POST
        if request.method == 'POST' and request.data.get('clear_all_data') == True:
            try:
                from django.db import transaction
                import os
                from django.conf import settings

                if not request.user.is_authenticated:
                    return Response({'error': 'Authentication required'}, status=401)

                with transaction.atomic():
                    # Get all inspections for this user
                    inspections = Inspection.objects.filter(created_by=request.user)
                    count = inspections.count()

                    if count == 0:
                        return Response({
                            'message': 'No inspections found to delete',
                            'deleted_count': 0
                        })

                    # Delete associated files
                    deleted_files = 0
                    for inspection in inspections:
                        # Delete photo files
                        for field_name in ['photo_marquage', 'photo_equipement']:
                            field_value = getattr(inspection, field_name, None)
                            if field_value:
                                try:
                                    if hasattr(field_value, 'path') and os.path.exists(field_value.path):
                                        os.remove(field_value.path)
                                        deleted_files += 1
                                except Exception as e:
                                    print(f'Warning: Could not delete {field_name}: {e}')

                        # Delete anomaly photos
                        if inspection.photos_anomalies:
                            for photo_path in inspection.photos_anomalies:
                                try:
                                    full_path = os.path.join(settings.MEDIA_ROOT, photo_path)
                                    if os.path.exists(full_path):
                                        os.remove(full_path)
                                        deleted_files += 1
                                except Exception as e:
                                    print(f'Warning: Could not delete anomaly photo {photo_path}: {e}')

                    # Delete all inspection records for this user
                    inspections.delete()

                    return Response({
                        'message': f'Successfully deleted {count} inspections and {deleted_files} associated files',
                        'deleted_count': count,
                        'deleted_files': deleted_files,
                        'action': 'data_cleared'
                    })

            except Exception as e:
                print(f"Error in clear data: {str(e)}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                return Response({
                    'error': str(e),
                    'error_type': type(e).__name__
                }, status=500)

        # Regular count functionality
        project = request.query_params.get('project')

        # Build query filters for authenticated user
        filters = {'created_by': request.user}

        # Add project filter if provided
        if project:
            filters['projet'] = project

        count = Inspection.objects.filter(**filters).count()
        return Response({'count': count})

    @action(detail=False, methods=['get', 'post'])
    def health_check(self, request):
        """Health check endpoint to test backend functionality and clear data"""
        try:
            from django.utils import timezone

            # Handle clear data request
            if request.method == 'POST' and request.data.get('clear_data') == True:
                if not request.user.is_authenticated:
                    return Response({'error': 'Authentication required'}, status=401)

                from django.db import transaction
                import os
                from django.conf import settings

                with transaction.atomic():
                    # Get all inspections for this user
                    inspections = Inspection.objects.filter(created_by=request.user)
                    count = inspections.count()

                    if count == 0:
                        return Response({
                            'message': 'No inspections found to delete',
                            'deleted_count': 0
                        })

                    # Delete associated files
                    deleted_files = 0
                    for inspection in inspections:
                        # Delete photo files
                        for field_name in ['photo_marquage', 'photo_equipement']:
                            field_value = getattr(inspection, field_name, None)
                            if field_value:
                                try:
                                    if hasattr(field_value, 'path') and os.path.exists(field_value.path):
                                        os.remove(field_value.path)
                                        deleted_files += 1
                                except Exception as e:
                                    print(f'Warning: Could not delete {field_name}: {e}')

                        # Delete anomaly photos
                        if inspection.photos_anomalies:
                            for photo_path in inspection.photos_anomalies:
                                try:
                                    full_path = os.path.join(settings.MEDIA_ROOT, photo_path)
                                    if os.path.exists(full_path):
                                        os.remove(full_path)
                                        deleted_files += 1
                                except Exception as e:
                                    print(f'Warning: Could not delete anomaly photo {photo_path}: {e}')

                    # Delete all inspection records for this user
                    inspections.delete()

                    return Response({
                        'message': f'Successfully deleted {count} inspections and {deleted_files} associated files',
                        'deleted_count': count,
                        'deleted_files': deleted_files,
                        'status': 'data_cleared'
                    })

            # Regular health check
            count = self.get_queryset().count()
            return Response({
                'status': 'healthy',
                'user': request.user.username if request.user.is_authenticated else 'anonymous',
                'inspection_count': count,
                'timestamp': str(timezone.now())
            })
        except Exception as e:
            return Response({
                'status': 'error',
                'error': str(e)
            }, status=500)

    @action(detail=False, methods=['post'])
    def clear_old_data(self, request):
        """Clear all existing inspections to start fresh with new F,G,C structure"""
        try:
            from django.db import transaction
            import os
            from django.conf import settings

            # Only allow authenticated users
            if not request.user.is_authenticated:
                return Response({'error': 'Authentication required'}, status=401)

            # Get confirmation from request
            confirm = request.data.get('confirm', False)
            if not confirm:
                return Response({
                    'error': 'Confirmation required',
                    'message': 'Send {"confirm": true} to proceed with deletion'
                }, status=400)

            with transaction.atomic():
                # Get all inspections for this user
                inspections = Inspection.objects.filter(created_by=request.user)
                count = inspections.count()

                if count == 0:
                    return Response({
                        'message': 'No inspections found to delete',
                        'deleted_count': 0
                    })

                # Delete associated files
                deleted_files = 0
                for inspection in inspections:
                    # Delete photo files
                    for field_name in ['photo_marquage', 'photo_equipement']:
                        field_value = getattr(inspection, field_name, None)
                        if field_value:
                            try:
                                if hasattr(field_value, 'path') and os.path.exists(field_value.path):
                                    os.remove(field_value.path)
                                    deleted_files += 1
                            except Exception as e:
                                print(f'Warning: Could not delete {field_name}: {e}')

                    # Delete anomaly photos
                    if inspection.photos_anomalies:
                        for photo_path in inspection.photos_anomalies:
                            try:
                                full_path = os.path.join(settings.MEDIA_ROOT, photo_path)
                                if os.path.exists(full_path):
                                    os.remove(full_path)
                                    deleted_files += 1
                            except Exception as e:
                                print(f'Warning: Could not delete anomaly photo {photo_path}: {e}')

                # Delete all inspection records for this user
                inspections.delete()

                return Response({
                    'message': f'Successfully deleted {count} inspections and {deleted_files} associated files',
                    'deleted_count': count,
                    'deleted_files': deleted_files
                })

        except Exception as e:
            print(f"Error in clear_old_data: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return Response({
                'error': str(e),
                'error_type': type(e).__name__
            }, status=500)

    @action(detail=True, methods=['get'])
    def debug_inspection(self, request, pk=None):
        """Debug endpoint to check inspection data"""
        try:
            inspection = self.get_object()
            return Response({
                'id': inspection.id,
                'fiche_num': inspection.fiche_num,
                'inspected_company_name': getattr(inspection, 'inspected_company_name', None),
                'projet': inspection.projet,
                'date': inspection.date,
                'user': request.user.username,
                'has_company_name': hasattr(inspection, 'inspected_company_name'),
                'company_name_value': getattr(inspection, 'inspected_company_name', 'NOT_SET')
            })
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['get'], renderer_classes=[PDFRenderer])
    def generate_pdf(self, request, pk=None):
        print(f"DEBUG: generate_pdf called with pk={pk}")
        try:
            inspection = self.get_object()
            print(f"DEBUG: Found inspection: {inspection}")
            print(f"DEBUG: Inspection mode_protection: {inspection.mode_protection}")
            print(f"DEBUG: Inspection niveaux: N1={inspection.niveau_1}, N2={inspection.niveau_2}, N3={inspection.niveau_3}")
            print(f"DEBUG: Inspection points type: {type(inspection.points)}")
            print(f"DEBUG: Inspection points sample: {str(inspection.points)[:200] if inspection.points else 'None'}")
        except Exception as e:
            print(f"ERROR: Could not get inspection object: {e}")
            import traceback
            print(f"ERROR: Traceback: {traceback.format_exc()}")
            from django.http import HttpResponse
            return HttpResponse(f"Inspection not found: {str(e)}", status=404, content_type='text/plain')

        # Create a BytesIO buffer to store the PDF
        buffer = BytesIO()
        print("DEBUG: Created BytesIO buffer")

        # Create the PDF document with A4 page size
        doc = SimpleDocTemplate(buffer, pagesize=A4,
                              rightMargin=1*cm, leftMargin=1*cm,
                              topMargin=1*cm, bottomMargin=1*cm)

        # Define custom styles
        styles = getSampleStyleSheet()

        # Custom styles for the form
        header_style = ParagraphStyle(
            'HeaderStyle',
            parent=styles['Normal'],
            fontSize=12,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER,
            spaceAfter=6
        )

        section_header_style = ParagraphStyle(
            'SectionHeaderStyle',
            parent=styles['Normal'],
            fontSize=10,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER,
            backColor=colors.lightgrey,
            borderWidth=1,
            borderColor=colors.black
        )

        normal_style = ParagraphStyle(
            'NormalStyle',
            parent=styles['Normal'],
            fontSize=9,
            fontName='Helvetica',
            alignment=TA_LEFT
        )

        # Create a specific style for points text with better wrapping
        points_style = ParagraphStyle(
            'PointsStyle',
            parent=styles['Normal'],
            fontSize=8,
            fontName='Helvetica',
            alignment=TA_LEFT,
            leftIndent=0,
            rightIndent=0,
            spaceAfter=2,
            leading=10
        )

        # Create a style for section headers
        section_header_style = ParagraphStyle(
            'SectionHeaderStyle',
            parent=styles['Heading1'],
            fontSize=12,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER,
            spaceAfter=6,
            spaceBefore=6
        )

        elements = []

        # Helper function to create clear, visible checkboxes
        def checkbox(checked):
            """Return a checkmark for checked items, blank for unchecked"""
            return "✓" if checked else ""

        # Helper function for simple checkbox display with checkmark
        def simple_checkbox(checked):
            """Return checkmark (✓) for checked, blank for unchecked"""
            return "✓" if checked else ""

        from reportlab.platypus import Image as ReportLabImage
        import os

        # Get company-specific logos
        performing_logo = None
        inspected_logo = None

        # Get the inspected company name from the inspection
        inspected_company_name = getattr(inspection, 'inspected_company_name', None)
        print(f"DEBUG: Inspected company name: {inspected_company_name}")
        print(f"DEBUG: Inspection object: {inspection}")
        print(f"DEBUG: Inspection fields: {[field.name for field in inspection._meta.fields]}")
        print(f"DEBUG: Has inspected_company_name field: {hasattr(inspection, 'inspected_company_name')}")

        # Also check all CompanyLogo objects for this user
        all_logos = CompanyLogo.objects.filter(user=request.user)
        print(f"DEBUG: All logos for user {request.user.username}: {list(all_logos.values('id', 'logo_type', 'company_name'))}")

        # Both logos should be company-specific now
        if inspected_company_name:
            # Get performing company logo for this specific company
            try:
                print(f"DEBUG: Searching for performing company logo with user={request.user.username}, company_name={inspected_company_name}")
                performing_logo_obj = CompanyLogo.objects.filter(
                    user=request.user,
                    logo_type='performing_company',
                    company_name=inspected_company_name
                ).first()
                print(f"DEBUG: Performing logo query result: {performing_logo_obj}")
                if performing_logo_obj and performing_logo_obj.logo:
                    print(f"DEBUG: Logo file path: {performing_logo_obj.logo.path}")
                    print(f"DEBUG: Logo file exists: {os.path.exists(performing_logo_obj.logo.path)}")
                    performing_logo = ReportLabImage(performing_logo_obj.logo.path, width=3*cm, height=3*cm)
                    print(f"DEBUG: Loaded performing company logo for {inspected_company_name}")
                else:
                    print(f"DEBUG: No performing company logo found for {inspected_company_name}")
                    print(f"DEBUG: performing_logo_obj is None: {performing_logo_obj is None}")
                    if performing_logo_obj:
                        print(f"DEBUG: performing_logo_obj.logo is None: {performing_logo_obj.logo is None}")
            except Exception as e:
                print(f"DEBUG: Error loading performing company logo: {e}")
                import traceback
                print(f"DEBUG: Traceback: {traceback.format_exc()}")

            # Get inspected company logo for this specific company
            try:
                print(f"DEBUG: Searching for inspected company logo with user={request.user.username}, company_name={inspected_company_name}")
                inspected_logo_obj = CompanyLogo.objects.filter(
                    user=request.user,
                    logo_type='inspected_company',
                    company_name=inspected_company_name
                ).first()
                print(f"DEBUG: Inspected logo query result: {inspected_logo_obj}")
                if inspected_logo_obj and inspected_logo_obj.logo:
                    print(f"DEBUG: Logo file path: {inspected_logo_obj.logo.path}")
                    print(f"DEBUG: Logo file exists: {os.path.exists(inspected_logo_obj.logo.path)}")
                    inspected_logo = ReportLabImage(inspected_logo_obj.logo.path, width=3*cm, height=3*cm)
                    print(f"DEBUG: Loaded inspected company logo for {inspected_company_name}")
                else:
                    print(f"DEBUG: No inspected company logo found for {inspected_company_name}")
                    print(f"DEBUG: inspected_logo_obj is None: {inspected_logo_obj is None}")
                    if inspected_logo_obj:
                        print(f"DEBUG: inspected_logo_obj.logo is None: {inspected_logo_obj.logo is None}")
            except Exception as e:
                print(f"DEBUG: Error loading inspected company logo: {e}")
                import traceback
                print(f"DEBUG: Traceback: {traceback.format_exc()}")
        else:
            print(f"DEBUG: No inspected company name specified - cannot load company-specific logos")

        # Summary of logo loading results
        print(f"DEBUG: Logo loading summary:")
        print(f"DEBUG: - performing_logo loaded: {performing_logo is not None}")
        print(f"DEBUG: - inspected_logo loaded: {inspected_logo is not None}")
        print(f"DEBUG: - inspected_company_name: {inspected_company_name}")

        # Fallback to default logo if performing company logo is not available
        if not performing_logo:
            print(f"DEBUG: No performing company logo found, falling back to default GESTS logo")
            try:
                default_logo_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'logo-gests.png')
                print(f"DEBUG: Default logo path: {default_logo_path}")
                print(f"DEBUG: Default logo exists: {os.path.exists(default_logo_path)}")
                if os.path.exists(default_logo_path):
                    performing_logo = ReportLabImage(default_logo_path, width=3*cm, height=3*cm)
                    print(f"DEBUG: Default logo loaded successfully")
                else:
                    print(f"DEBUG: Default logo file not found")
            except Exception as e:
                print(f"DEBUG: Error loading default logo: {e}")
        else:
            print(f"DEBUG: Using company-specific performing logo")

        # Create header with both logos and title
        header_data = [
            [
                performing_logo if performing_logo else '',
                inspected_logo if inspected_logo else '',
                Paragraph('<b>INSPECTION VISUELLE & DE PRÈS</b>', header_style)
            ]
        ]

        # Create header table with proper column widths for both logos and title
        header_table = Table(header_data, colWidths=[4*cm, 4*cm, 10*cm])
        header_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),  # Performing company logo centered
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),  # Inspected company logo centered
            ('ALIGN', (2, 0), (2, 0), 'CENTER'),  # Title centered
            ('FONTSIZE', (0, 0), (-1, -1), 9),
        ]))
        elements.append(header_table)
        elements.append(Spacer(1, 0.3*cm))

        # File information section
        file_info_data = [
            [
                f'Fiche n° : {inspection.fiche_num or ""}',
                f'Date : {inspection.date or ""}',
                f'Projet : {inspection.projet or ""}'
            ]
        ]

        file_info_table = Table(file_info_data, colWidths=[6*cm, 4*cm, 8*cm])
        file_info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(file_info_table)
        elements.append(Spacer(1, 0.2*cm))

        # Inspector Information section
        inspector_info_data = [
            [
                f'Date de précédente inspection : {inspection.date_precedente_inspection or ""}',
                'Standard : IEC 60079-17 V2023'
            ]
        ]

        inspector_info_table = Table(inspector_info_data, colWidths=[9*cm, 9*cm])
        inspector_info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(inspector_info_table)
        elements.append(Spacer(1, 0.1*cm))

        # Inspector details section
        inspector_details_data = [
            [
                f'Inspecteur : {inspection.inspecteur or ""}',
                f'Qualifications : {inspection.qualifications or ""}',
                f'Numéro de certificat : {inspection.numero_certificat or ""}'
            ]
        ]

        inspector_details_table = Table(inspector_details_data, colWidths=[6*cm, 6*cm, 6*cm])
        inspector_details_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(inspector_details_table)
        elements.append(Spacer(1, 0.2*cm))

        # General Information section
        general_info_header = [
            [Paragraph('<b>Informations générales</b>', section_header_style),
             Paragraph('<b>Emplacement</b>', section_header_style)]
        ]

        # Handle custom equipment and unit fields with safe attribute access
        equipement_display = (
            getattr(inspection, 'equipement_custom', None)
            if inspection.equipement == 'custom' and hasattr(inspection, 'equipement_custom') and getattr(inspection, 'equipement_custom', None)
            else inspection.equipement
        )
        unite_display = (
            getattr(inspection, 'unite_custom', None)
            if inspection.unite == 'custom' and hasattr(inspection, 'unite_custom') and getattr(inspection, 'unite_custom', None)
            else inspection.unite
        )

        general_info_data = [
            [f'Equipement : {equipement_display or ""}', f'Unité : {unite_display or ""}'],
            [f'Repère / TAG : {inspection.tag or ""}', f'Localisation : {inspection.localisation or ""}'],
            [f'Constructeur : {inspection.constructeur or ""}', f'Zone ATEX : {inspection.zone_atex or ""}'],
            [f'Modèle / Type : {inspection.model or ""}', f'Groupe de gaz : {inspection.groupe_gaz or ""}'],
            [f'N° de série : {inspection.numero_serie or ""}', f'Date d\'installation : {inspection.date_installation or ""}'],
            [f'Age : {inspection.age or ""}', f'Classe de T : {inspection.classe_t or ""}'],
            [f'P (W) : {inspection.puissance or ""} | I (A) : {inspection.courant or ""} | U (V) : {inspection.tension or ""}', '']
        ]

        # Combine header and data
        general_table_data = general_info_header + general_info_data

        general_table = Table(general_table_data, colWidths=[9*cm, 9*cm])
        general_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ]))
        elements.append(general_table)
        elements.append(Spacer(1, 0.2*cm))

        # ATEX specific information section
        atex_header = [
            [Paragraph('<b>Informations spécifiques ATEX</b>', section_header_style)]
        ]

        atex_data = [
            [f'Marquage ATEX G : {inspection.marquage_atex_g or ""}'],
            [f'Marquage ATEX D : {inspection.marquage_atex_d or ""}'],
            [f'Marquage US : {getattr(inspection, "marquage_us", "") or ""}'],
            [f'Type de marquage : {inspection.type_marquage or ""} | Mode de protection : {inspection.mode_protection or ""}'],
            [f'N° organisme notifié : {inspection.organisme_notifie or ""} | IP : {inspection.ip or ""} | NEMA : {inspection.nema or ""}'],
            [f'N° de certificat : {inspection.certificat or ""} | T amb : {inspection.tamb_min or ""} ≤ T amb ≤ {inspection.tamb_max or ""}']
        ]

        atex_table_data = atex_header + atex_data
        atex_table = Table(atex_table_data, colWidths=[18*cm])
        atex_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
        ]))
        elements.append(atex_table)
        elements.append(Spacer(1, 0.2*cm))

        # Equipment adequate for ATEX zone section
        # Clean checkbox display: Shows ✓ for checked, blank for unchecked
        def create_atex_text(checked, label):
            if checked:
                return f'Oui <font color="green">✓</font>' if label == 'Oui' else f'Non <font color="green">✓</font>'
            else:
                return f'Oui  ' if label == 'Oui' else f'Non  '  # Two spaces for alignment

        atex_adequate_data = [
            [Paragraph('<b>Équipement adéquat à la zone ATEX :</b>', normal_style),
             Paragraph(create_atex_text(inspection.atex_oui, 'Oui'), normal_style),
             Paragraph(create_atex_text(inspection.atex_non, 'Non'), normal_style)]
        ]

        atex_adequate_table = Table(atex_adequate_data, colWidths=[12*cm, 3*cm, 3*cm])
        atex_adequate_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
        ]))
        elements.append(atex_adequate_table)
        elements.append(Spacer(1, 0.2*cm))

        # Visual and Close Inspection section
        inspection_header = [
            [Paragraph('<b>Inspection Visuelle et de Près</b>', section_header_style)]
        ]

        # Photos section
        photos_data = [
            [Paragraph('<b>Photos de l\'équipement</b>', normal_style)]
        ]

        # Access section
        access_header = [
            [Paragraph('<b>Accès</b>', normal_style)]
        ]

        # Access checkboxes - clean display with checkmarks for selected items
        # Create mixed content with green checkmarks and black text
        def create_access_text(checked, label):
            if checked:
                return f'<font color="green">✓</font> {label}'
            else:
                return f'  {label}'  # Two spaces for alignment when no checkmark

        access_data = [
            [
                Paragraph(create_access_text(inspection.acces_inaccessible, 'Appareil Inaccessible'), normal_style),
                Paragraph(create_access_text(inspection.acces_calorifuge, 'Appareil sous Calorifuge / Ignifuge'), normal_style),
                Paragraph(create_access_text(inspection.acces_peinte, 'Plaque Peinte'), normal_style)
            ],
            [
                Paragraph(create_access_text(inspection.acces_inaccessible_plaque, 'Plaque Inaccessible'), normal_style),
                Paragraph(create_access_text(inspection.acces_illisible, 'Plaque Illisible'), normal_style),
                Paragraph(create_access_text(inspection.acces_pas_plaque, 'Pas de plaque'), normal_style)
            ]
        ]

        # Combine inspection sections
        inspection_table_data = inspection_header + photos_data + access_header + access_data

        inspection_table = Table(inspection_table_data, colWidths=[6*cm, 6*cm, 6*cm])
        inspection_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('SPAN', (0, 0), (-1, 0)),  # Span header across all columns
            ('SPAN', (0, 1), (-1, 1)),  # Span photos section across all columns
            ('SPAN', (0, 2), (-1, 2)),  # Span access header across all columns
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 2), (0, 2), 'Helvetica-Bold'),
        ]))
        elements.append(inspection_table)
        elements.append(Spacer(1, 0.2*cm))

        # Niveau selection section
        niveau_header = [
            [Paragraph('<b>Niveaux d\'inspection sélectionnés</b>', section_header_style)]
        ]

        niveau_selections = []
        if hasattr(inspection, 'niveau_1') and inspection.niveau_1:
            niveau_selections.append('Niveau 1')
        if hasattr(inspection, 'niveau_2') and inspection.niveau_2:
            niveau_selections.append('Niveau 2')
        if hasattr(inspection, 'niveau_3') and inspection.niveau_3:
            niveau_selections.append('Niveau 3')

        niveau_text = ', '.join(niveau_selections) if niveau_selections else 'Aucun niveau sélectionné'

        niveau_data = [
            [niveau_text]
        ]

        niveau_table_data = niveau_header + niveau_data
        niveau_table = Table(niveau_table_data, colWidths=[18*cm])
        niveau_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
        ]))
        elements.append(niveau_table)
        elements.append(Spacer(1, 0.2*cm))

        # Points to verify section with clear column headers
        points_header = [
            [
                Paragraph('<b>Points à vérifier</b>', normal_style),
                Paragraph('<b>Correct</b>', normal_style),
                Paragraph('<b>Défaut</b>', normal_style),
                Paragraph('<b>F</b>', normal_style),
                Paragraph('<b>G</b>', normal_style),
                Paragraph('<b>C</b>', normal_style),
                Paragraph('<b>Remarques</b>', normal_style)
            ]
        ]

        # Process inspection points data from the actual form submission
        # The points data comes from the form's points object
        points_data = []

        # Complete point definitions from InspectionFormPage.js - EXACT TEXT
        point_labels = {
            # Les installations Ex "d", Ex "e", Ex "n" et Ex "t/tD"  - GÉNÉRALITÉS (TOUS LES MATÉRIELS)
            'mat_approprié': 'Le matériel est approprié aux exigences relatives au niveau de protection/zone de l\'emplacement concerné',
            'groupe_correct': 'Le groupe de matériel est correct',
            'classe_temp': 'La classe de température du matériel est correcte (uniquement pour les gaz) (n)',
            'temp_surface': 'La température de surface maximale du matériel est correcte',
            'degre_protection': 'Le degré de protection (degré IP) du matériel est approprié au niveau de protection/groupe/conductivité',
            'identification_circuit': 'L\'identification du circuit du matériel est disponible',
            'enveloppe_verre': 'L\'enveloppe, les parties en verre et les garnitures et/ou les matériaux d\'étanchéité verre sur métal sont satisfaisants',
            'modification_non_autorisee': 'Il n\'y a pas de modification non autorisée visible',
            'boulons_entrees': 'Les boulons, les dispositifs d\'entrées de câbles (directes et indirectes) et les éléments d\'obturation sont d\'un type correct et sont complets et serrés – vérification physique – vérification visuelle',
            'couvercles_filetes': 'Les couvercles filetés sur les enveloppes sont du type correct et sont serrés et fixés solidement – vérification physique – vérification visuelle',
            'dispositifs_respiration': 'Les dispositifs de respiration et de drainage sont satisfaisants (d,e,n)',
            'identification_correcte': 'L\'identification du circuit du matériel est correcte',
            'surfaces_joints': 'Les surfaces des joints plans sont propres et non endommagées et les garnitures éventuelles sont satisfaisantes et correctement positionnées (d)',
            'etat_garnitures': 'L\'état des garnitures des enveloppes est satisfaisant (d)',
            'penetration_eau': 'Il n\'y a aucun signe visible de pénétration d\'eau ou de poussière dans l\'enveloppe conformément au degré IP (d)',
            'dimensions_espaces': 'Les dimensions des espaces entre joints à brides sont : - dans les limites conformément à la documentation du constructeur, ou - dans les limites des valeurs maximales autorisées par la norme de construction applicable au moment de l\'installation, ou - dans les limites des valeurs maximales autorisées par la documentation du site (d)',
            'connexions_serrees': 'Les connexions électriques sont serrées (e,n,t/tD)',
            'bornes_non_utilisees': 'Les bornes non utilisées sont serrées (e,n)',
            'dispositifs_scellement': 'Les dispositifs enfermés et de scellement hermétique ne sont pas endommagés (n)',
            'composants_encapsules': 'Les composants encapsulés ne sont pas endommagés (e,n)',
            'composants_ignifuges': 'Les composants ignifuges ne sont pas endommagés (e,n)',
            'enveloppes_respiration': 'Les enveloppes à respiration limitée sont satisfaisantes (mode "nR" uniquement) (n)',
            'port_essai': 'Le port d\'essai, s\'il est installé, est fonctionnel (mode "nR" uniquement) (n)',
            'operation_respiration': 'L\'opération de respiration est satisfaisante (mode "nR" uniquement) (d,e,n)',

            # MATÉRIEL PARTICULIER (ÉCLAIRAGE)
            'lampes_fluo': 'Les lampes fluorescentes n\'indiquent pas d\'effets EOL (e,n,t,td)',
            'lampes_dhi': 'Les lampes à décharge à haute intensité (DHI) n\'indiquent pas d\'effets EOL',
            'type_lampes': 'Le type, les caractéristiques assignées, la configuration des broches et la position des lampes sont corrects',

            # MATÉRIEL PARTICULIER (MOTEURS)
            'distance_ventilateur': 'La distance entre le ventilateur et l\'enveloppe et/ou les couvercles est suffisante, les systèmes de refroidissement ne sont pas endommagés, les assises de moteur ne présentent aucune indentation ou fissure, etc.',
            'circulation_air': 'Aucun obstacle à la circulation de l\'air de ventilation',
            'resistance_isolement': 'La résistance d\'isolement (IR) des enroulements du moteur est satisfaisante',

            # INSTALLATION - GÉNÉRALITÉS
            'dommage_cables': 'Il n\'y a pas de dommage apparent aux câbles',
            'obturation_traversees': 'L\'obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante',
            'liaisons_terre': 'Les liaisons à la terre, y compris toute liaison à la terre supplémentaire, sont satisfaisantes (par exemple, les connexions sont serrées et les conducteurs ont une section suffisante) : – inspection physique – inspection visuelle',
            'engorgements_joints': 'Les engorgements adjacents aux joints à brides ignifuges sont conformes aux exigences de la CEI 60079-14 (d)',
            'installation_variable': 'L\'installation à tension/fréquence variables est conforme à la documentation',
            'type_cable': 'Le type de câble est approprié',
            'boitiers_arret': 'Les boîtiers d\'arrêt et les boîtiers de câbles sont correctement remplis',
            'integrite_conduits': 'L\'intégrité des systèmes de conduits et l\'interface avec les systèmes mixtes sont maintenues',
            'impedance_boucle': 'L\'impédance de boucle du défaut (schémas TN) ou la résistance à la terre (schémas IT) est satisfaisante',
            'protection_electrique': 'Les dispositifs automatiques de protection électriques sont correctement réglés (le réarmement automatique n\'est pas possible)',
            'protection_electrique_chauffage': 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées dans la CEI 60079-14 (d,e)',
            'conditions_utilisation': 'Les conditions particulières d\'utilisation (s\'il y a lieu) sont respectées',
            'extremites_cables': 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées',

            # INSTALLATION - SYSTÈMES DE CHAUFFAGE
            'reglage_coupure': 'Le réglage de la coupure de sécurité est scellé (d,e)',
            'reinitialisation_coupure': 'La réinitialisation d\'une coupure de sécurité du système de chauffage n\'est possible qu\'avec un outil (d,e)',
            'reinitialisation_automatique': 'La réinitialisation automatique n\'est pas possible (d,e)',
            'capteurs_temperature': 'Les capteurs de température fonctionnent conformément à la documentation du constructeur (t)',
            'dispositifs_coupure': 'Les dispositifs de coupure de sécurité fonctionnent conformément à la documentation du constructeur (t)',
            'reinitialisation_defaut': 'La réinitialisation d\'une coupure de sécurité dans des conditions de défaut est empêchée (d,e)',
            'coupure_independante': 'La coupure de sécurité est indépendante du système de commande (d,e)',
            'capteur_niveau': 'Le capteur de niveau est installé et correctement réglé, si nécessaire (d,e)',
            'capteur_debit': 'Le capteur de débit est installé et correctement réglé, si nécessaire (d,e)',

            # MOTEURS
            'protection_moteur': 'Les dispositifs de protection du moteur fonctionnent dans les limites de temps autorisées tE ou iA (e)',

            # ENVIRONNEMENT
            'protection_materiel': 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles',
            'accumulation_poussiere': 'Il n\'y a pas d\'accumulation anormale de poussière et de saleté',
            'isolation_electrique': 'L\'isolation électrique est propre et sèche (e,t/tD)',

            # Les installations Ex "i" - MATERIEL
            'doc_materiel_appropriate': 'La documentation du matériel et/ou du circuit est appropriée au niveau de protection / exigences de la zone concernée',
            'materiel_specifie': 'Le matériel installé est celui qui est spécifié dans la documentation',
            'categorie_groupe_correct': 'La catégorie et le groupe du circuit et/ou du matériel sont corrects',
            'degre_ip_groupe3': 'Le degré IP du matériel est approprié au matériau de groupe III présent',
            'classe_temp_materiel': 'La classe de température du matériel est correcte',
            'plage_temp_ambiante': 'La plage de températures ambiantes de l\'appareil est correcte pour l\'installation',
            'plage_temp_service': 'La plage de températures de service de l\'appareil est correcte pour l\'installation',
            'installation_reperee': 'L\'installation est clairement repérée',
            'presse_etoupes_type': 'Les presse-étoupes et éléments d\'obturation sont du type correct et sont complets et serrés – vérification physique – vérification visuelle',
            'modification_non_autorisee_i': 'Il n\'y a pas de modification non autorisée visible',
            'barrieres_securite': 'Les barrières de sécurité à diodes, les isolateurs galvaniques, les relais et autres dispositifs de limitation de l\'énergie sont d\'un type approuvé, sont installés conformément aux exigences de certification et sont convenablement mis à la terre si cela est exigé',
            'tension_max_um': 'La tension maximale Um de l\'appareil associé n\'est pas dépassée',
            'enveloppe_verre_i': 'L\'enveloppe, les parties en verre et les garnitures et/ou les matériaux d\'étanchéité verre sur métal sont satisfaisants',
            'pas_modif_non_autorisee': 'Il n\'y a pas de modification non autorisée',
            'etat_garnitures_env': 'L\'état des garnitures des enveloppes est satisfaisant',
            'connexions_serrees_i': 'Les connexions électriques sont serrées',
            'cartes_propres': 'Les cartes imprimées sont propres et non endommagées',

            # Les installations Ex "i" - INSTALLATION
            'dommage_cables_i': 'Il n\'y a pas de dommage apparent aux câbles',
            'obturation_traversees_i': 'L\'obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante',
            'cables_documentation': 'Les câbles sont installés conformément à la documentation',
            'ecrans_documentation': 'Les écrans des câbles sont mis à la terre conformément à la documentation',
            'connexions_point_point': 'Les connexions point à point sont toutes correctes (inspection initiale uniquement)',
            'continuite_terre': 'La continuité des liaisons à la terre est satisfaisante (par exemple, les connexions sont serrées, les conducteurs ont une section suffisante) pour les circuits non isolés galvaniquement',
            'liaison_terre_integrite': 'Les liaisons à la terre n\'affectent pas l\'intégrité du mode de protection',
            'mise_a_la_terre_sic': 'La mise à la terre du circuit de sécurité intrinsèque est satisfaisante',
            'resistance_isolement_i': 'La résistance d\'isolement est satisfaisante',
            'separation_circuits': 'La séparation entre les circuits de sécurité intrinsèque et les circuits de sécurité non intrinsèque est assurée lorsque ces circuits sont dans un même boîtier de distribution ou dans un même boîtier relais',
            'protection_cc_alim': 'La protection contre les courts-circuits de l\'alimentation est conforme à la documentation',
            'conditions_utilisation_i': 'Les conditions particulières d\'utilisation (s\'il y a lieu) sont respectées',
            'extremites_cables_prot': 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées',

            # Les installations Ex "i" - ENVIRONNEMENT
            'protection_materiel_i': 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles',
            'accumulation_poussiere_ext': 'Il n\'y a pas d\'accumulation anormale extérieure de poussière et de saleté',

            # Les installations Ex "o" - ÉQUIPMENT
            'mat_approprie_o': 'Le matériel est approprié aux exigences relatives au niveau de protection/zone de l\'emplacement concerné',
            'groupe_correct_o': 'Le groupe de matériel est correct',
            'classe_temp_o': 'La classe de température du matériel est correcte',
            'identification_circuit_o': 'L\'identification du circuit du matériel est disponible',
            'enveloppe_verre_o': 'L\'enveloppe, les parties en verre et les garnitures et/ou les matériaux d\'étanchéité verre sur métal sont satisfaisants',
            'modification_non_autorisee_o': 'Il n\'y a pas de modification non autorisée visible',
            'boulons_entrees_o': 'Les boulons, les dispositifs d\'entrées de câbles (directes et indirectes) et les éléments d\'obturation sont d\'un type correct et sont complets et serrés – vérification physique – vérification visuelle',
            'enveloppes_scellees_o': 'Les enveloppes portant la mention « Scellée de façon permanente » ne présentent aucun signe visible d\'ouverture.',
            'critere_liquide_protection': 'Critères maximum/minimum du liquide de protection a) Le niveau du liquide de protection doit être inférieur ou égal au niveau maximum autorisé et supérieur au niveau minimum autorisé ; b) L\'angle de fonctionnement maximum par rapport à l\'horizontale de l\'équipement doit être conforme. Lorsque qu\'une jauge est fournie, celle-ci doit être fixée en position de mesure et son étanchéité doit être satisfaisante.',
            'fonctionnement_tele_signalisation': 'Le fonctionnement du dispositif de télésignalisation du niveau du liquide de protection est satisfaisant.',
            'programme_nettoyage_liquide': 'Le programme de nettoyage, de filtration ou de remplacement du liquide de protection du dispositif de commutation, après un nombre donné de manœuvres normales ou d\'interruptions de courants de défaut, est documenté.',
            'identification_circuits_o': 'L\'identification des circuits de l\'équipement est correcte.',
            'pas_modif_non_autorisee_o': 'Aucune modification non autorisée n\'a été constatée.',
            'connexions_serrees_o': 'Les connexions électriques sont bien serrées.',
            'etat_joints_etancheite': 'L\'état des joints d\'étanchéité de l\'enveloppe est satisfaisant.',
            'dispositifs_respiration_o': 'Les dispositifs de respiration et de drainage sont satisfaisants. Le programme d\'entretien du fabricant concernant les exigences liées à l\'agent de séchage a été respecté et dûment documenté.',
            'surpression_enveloppe': 'Les dispositifs de surpression des enveloppes scellées sont en bon état de fonctionnement.',
            'niveau_liquide_o': 'Pour les enveloppes destinées à être ouvertes, le niveau du liquide de protection dans le cadre du mode de protection de type "o" est correct.',

            # Les installations Ex "o" - INSTALLATION - GÉNÉRALITÉS
            'dommage_cables_o': 'Il n\'y a pas de dommage apparent aux câbles',
            'obturation_traversees_o': 'L\'obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante',
            'liaisons_terre_o': 'Les liaisons à la terre, y compris toute liaison à la terre supplémentaire, sont satisfaisantes (par exemple, les connexions sont serrées et les conducteurs ont une section suffisante) – inspection physique – inspection visuelle',
            'installation_variable_o': 'L\'installation à tension/fréquence variables est conforme à la documentation',
            'type_cable_o': 'Le type de câble est approprié',
            'boitiers_coupe_feu': 'Les dispositifs coupe-feu, les boites de jonction et les entrées de câbles sont correctement remplis',
            'integrite_conduits_o': 'L\'intégrité des systèmes de conduits et l\'interface avec les systèmes mixtes sont maintenues',
            'impedance_boucle_o': 'L\'impédance de boucle du défaut (schémas TN) ou la résistance à la terre (schémas IT) est satisfaisante',
            'protection_electrique_o': 'Les dispositifs automatiques de protection électriques sont correctement réglés (le réarmement automatique n\'est pas possible)',
            'protection_electrique_autorisee': 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées',
            'conditions_utilisation_o': 'Les conditions particulières d\'utilisation (s\'il y a lieu) sont respectées',
            'extremites_cables_prot_o': 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées.',

            # Les installations Ex "o" - INSTALLATION – SYSTÈMES DE CHAUFFAGE
            'reglage_coupure_o': 'Le réglage de la coupure de sécurité est scellé.',
            'reinitialisation_coupure_outil': 'La réinitialisation d\'une coupure de sécurité du système de chauffage n\'est possible qu\'avec un outil',
            'reinitialisation_auto_o': 'La réinitialisation automatique n\'est pas possible',
            'capteurs_temperature_o': 'Les capteurs de température fonctionnent conformément à la documentation du constructeur',
            'dispositifs_coupure_o': 'Les dispositifs de coupure de sécurité fonctionnent conformément à la documentation du constructeur',
            'reinitialisation_defaut_o': 'La réinitialisation d\'une coupure de sécurité dans des conditions de défaut est empêchée',
            'coupure_independante_o': 'La coupure de sécurité est indépendante du système de commande',
            'capteur_niveau_o': 'Le capteur de niveau est installé et correctement réglé, si nécessaire',
            'capteur_debit_o': 'Le capteur de débit est installé et correctement réglé, si nécessaire',

            # Les installations Ex "o" - ENVIRONNEMENT
            'protection_materiel_o': 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles',
            'accumulation_poussiere_o': 'Il n\'y a pas d\'accumulation anormale de poussière et de saleté',
            'isolation_electrique_o': 'L\'isolation électrique est propre et sèche',

            # Les installations Ex "p" et "pD" - MATERIEL
            'mat_approprie_p': 'Le matériel est approprié au niveau de protection/aux exigences de la zone concernée',
            'groupe_correct_p': 'Le groupe de matériel est correct',
            'classe_temp_p': 'La classe de température du matériel ou la température de surface est correcte',
            'identification_circuit_p': 'L\'identification du circuit du matériel est disponible',
            'enveloppe_verre_p': 'L\'enveloppe, les parties en verre et les garnitures et/ou matériaux d\'étanchéité verre sur métal sont satisfaisants',
            'modification_non_autorisee_p': 'Il n\'y a pas de modification non autorisée visible',
            'identification_circuit_correcte_p': 'L\'identification du circuit du matériel est correcte',
            'pas_modif_non_autorisee_p': 'Il n\'y a pas de modification non autorisée',
            'type_lampes_p': 'Le type, les caractéristiques assignées et la position des lampes sont corrects',

            # Les installations Ex "p" et "pD" - INSTALLATION
            'dommage_cables_p': 'Il n\'y a pas de dommage apparent aux câbles',
            'liaisons_terre_p': 'Les liaisons à la terre, y compris toute liaison à la terre supplémentaire, sont satisfaisantes, par exemple les connexions sont serrées et les conducteurs ont une section suffisante – vérification physique – vérification visuelle',
            'conduites_tubes_p': 'Les conduites, tubes et enveloppes sont en bon état',
            'gaz_impuretes_p': 'Le gaz de protection ne contient pas d\'impuretés',
            'pression_debit_gaz': 'La pression et/ou le débit du gaz de protection sont appropriés',
            'type_cable_p': 'Le type de câble est approprié',
            'impedance_boucle_it': 'L\'impédance de boucle du défaut (schémas TN) ou la résistance à la terre (schémas IT) est satisfaisante',
            'protection_electrique_p': 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées',
            'protection_reglee_p': 'Les dispositifs automatiques de protection électriques sont correctement réglés fonctionnent cirrectement',
            'temp_entree_gaz': 'La température d\'entrée du gaz de protection est inférieure au maximum spécifié',
            'barrieres_etincelles': 'Les conditions des barrières contre les étincelles et les particules des conduits pour évacuer le gaz des emplacements dangereux sont satisfaisantes',
            'conditions_utilisation_p': 'Les conditions particulières d\'utilisation (s\'il y a lieu) sont respectées',

            # Les installations Ex "p" et "pD" - ENVIRONNEMENT
            'protection_materiel_p': 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles',
            'accumulation_poussiere_p': 'Il n\'y a pas d\'accumulation anormale de poussière et de saleté',
        }

        # Define niveau requirements for each point (EXACT match from frontend)
        point_niveaux = {
            # Les installations Ex "d", Ex "e", Ex "n" et Ex "t/tD" - GÉNÉRALITÉS (TOUS LES MATÉRIELS)
            'mat_approprié': [],  # niveaux: []
            'groupe_correct': [2, 3],  # niveaux: [2,3]
            'classe_temp': [2, 3],  # niveaux: [2,3]
            'temp_surface': [2, 3],  # niveaux: [2,3]
            'degre_protection': [],  # niveaux: []
            'identification_circuit': [],  # niveaux: []
            'enveloppe_verre': [],  # niveaux: []
            'modification_non_autorisee': [1, 2],  # niveaux: [1,2]
            'boulons_entrees': [],  # niveaux: []
            'couvercles_filetes': [],  # niveaux: []
            'dispositifs_respiration': [2, 3],  # niveaux: [2,3]
            'identification_correcte': [3],  # niveaux: [3]
            'surfaces_joints': [3],  # niveaux: [3]
            'etat_garnitures': [3],  # niveaux: [3]
            'penetration_eau': [3],  # niveaux: [3]
            'dimensions_espaces': [3],  # niveaux: [3]
            'connexions_serrees': [3],  # niveaux: [3]
            'bornes_non_utilisees': [3],  # niveaux: [3]
            'dispositifs_scellement': [3],  # niveaux: [3]
            'composants_encapsules': [3],  # niveaux: [3]
            'composants_ignifuges': [3],  # niveaux: [3]
            'enveloppes_respiration': [3],  # niveaux: [3]
            'port_essai': [3],  # niveaux: [3]
            'operation_respiration': [3],  # niveaux: [3]

            # MATÉRIEL PARTICULIER (ÉCLAIRAGE)
            'lampes_fluo': [],  # niveaux: []
            'lampes_dhi': [],  # niveaux: []
            'type_lampes': [3],  # niveaux: [3]

            # MATÉRIEL PARTICULIER (MOTEURS)
            'distance_ventilateur': [],  # niveaux: []
            'circulation_air': [3],  # niveaux: [3]
            'resistance_isolement': [3],  # niveaux: [3]

            # INSTALLATION - GÉNÉRALITÉS
            'dommage_cables': [],  # niveaux: []
            'obturation_traversees': [],  # niveaux: []
            'liaisons_terre': [],  # niveaux: []
            'engorgements_joints': [],  # niveaux: []
            'installation_variable': [],  # niveaux: []
            'type_cable': [2],  # niveaux: [2]
            'boitiers_arret': [3],  # niveaux: [3]
            'integrite_conduits': [3],  # niveaux: [3]
            'impedance_boucle': [3],  # niveaux: [3]
            'protection_electrique': [3],  # niveaux: [3]
            'protection_electrique_chauffage': [3],  # niveaux: [3]
            'conditions_utilisation': [3],  # niveaux: [3]
            'extremites_cables': [3],  # niveaux: [3]

            # INSTALLATION - SYSTÈMES DE CHAUFFAGE
            'reglage_coupure': [2, 3],  # niveaux: [2,3]
            'reinitialisation_coupure': [2, 3],  # niveaux: [2,3]
            'reinitialisation_automatique': [2, 3],  # niveaux: [2,3]
            'capteurs_temperature': [3],  # niveaux: [3]
            'dispositifs_coupure': [3],  # niveaux: [3]
            'reinitialisation_defaut': [3],  # niveaux: [3]
            'coupure_independante': [3],  # niveaux: [3]
            'capteur_niveau': [3],  # niveaux: [3]
            'capteur_debit': [3],  # niveaux: [3]

            # MOTEURS
            'protection_moteur': [3],  # niveaux: [3]

            # ENVIRONNEMENT
            'protection_materiel': [],  # niveaux: []
            'accumulation_poussiere': [],  # niveaux: []
            'isolation_electrique': [3],  # niveaux: [3]

            # Les installations Ex "i" - MATERIEL
            'doc_materiel_appropriate': [],  # niveaux: []
            'materiel_specifie': [2, 3],  # niveaux: [2,3]
            'categorie_groupe_correct': [2, 3],  # niveaux: [2,3]
            'degre_ip_groupe3': [2, 3],  # niveaux: [2,3]
            'classe_temp_materiel': [2, 3],  # niveaux: [2,3]
            'plage_temp_ambiante': [2, 3],  # niveaux: [2,3]
            'plage_temp_service': [2, 3],  # niveaux: [2,3]
            'installation_reperee': [2, 3],  # niveaux: [2,3]
            'presse_etoupes_type': [],  # niveaux: []
            'modification_non_autorisee_i': [1, 2],  # niveaux: [1,2]
            'barrieres_securite': [],  # niveaux: []
            'tension_max_um': [2, 3],  # niveaux: [2,3]
            'enveloppe_verre_i': [3],  # niveaux: [3]
            'pas_modif_non_autorisee': [3],  # niveaux: [3]
            'etat_garnitures_env': [3],  # niveaux: [3]
            'connexions_serrees_i': [3],  # niveaux: [3]
            'cartes_propres': [3],  # niveaux: [3]

            # Les installations Ex "i" - INSTALLATION
            'dommage_cables_i': [],  # niveaux: []
            'obturation_traversees_i': [],  # niveaux: []
            'cables_documentation': [3],  # niveaux: [3]
            'ecrans_documentation': [3],  # niveaux: [3]
            'connexions_point_point': [3],  # niveaux: [3]
            'continuite_terre': [3],  # niveaux: [3]
            'liaison_terre_integrite': [3],  # niveaux: [3]
            'mise_a_la_terre_sic': [3],  # niveaux: [3]
            'resistance_isolement_i': [3],  # niveaux: [3]
            'separation_circuits': [3],  # niveaux: [3]
            'protection_cc_alim': [3],  # niveaux: [3]
            'conditions_utilisation_i': [3],  # niveaux: [3]
            'extremites_cables_prot': [3],  # niveaux: [3]

            # Les installations Ex "i" - ENVIRONNEMENT
            'protection_materiel_i': [],  # niveaux: []
            'accumulation_poussiere_ext': [],  # niveaux: []

            # Les installations Ex "o" - ÉQUIPMENT
            'mat_approprie_o': [],  # niveaux: []
            'groupe_correct_o': [2, 3],  # niveaux: [2,3]
            'classe_temp_o': [2, 3],  # niveaux: [2,3]
            'identification_circuit_o': [],  # niveaux: []
            'enveloppe_verre_o': [],  # niveaux: []
            'modification_non_autorisee_o': [1, 2],  # niveaux: [1,2]
            'boulons_entrees_o': [],  # niveaux: []
            'enveloppes_scellees_o': [],  # niveaux: []
            'critere_liquide_protection': [],  # niveaux: []
            'fonctionnement_tele_signalisation': [],  # niveaux: []
            'programme_nettoyage_liquide': [],  # niveaux: []
            'identification_circuits_o': [3],  # niveaux: [3]
            'pas_modif_non_autorisee_o': [3],  # niveaux: [3]
            'connexions_serrees_o': [3],  # niveaux: [3]
            'etat_joints_etancheite': [3],  # niveaux: [3]
            'dispositifs_respiration_o': [3],  # niveaux: [3]
            'surpression_enveloppe': [3],  # niveaux: [3]
            'niveau_liquide_o': [3],  # niveaux: [3]

            # Les installations Ex "o" - INSTALLATION - GÉNÉRALITÉS
            'dommage_cables_o': [],  # niveaux: []
            'obturation_traversees_o': [],  # niveaux: []
            'liaisons_terre_o': [],  # niveaux: []
            'installation_variable_o': [],  # niveaux: []
            'type_cable_o': [3],  # niveaux: [3]
            'boitiers_coupe_feu': [3],  # niveaux: [3]
            'integrite_conduits_o': [3],  # niveaux: [3]
            'impedance_boucle_o': [3],  # niveaux: [3]
            'protection_electrique_o': [3],  # niveaux: [3]
            'protection_electrique_autorisee': [3],  # niveaux: [3]
            'conditions_utilisation_o': [3],  # niveaux: [3]
            'extremites_cables_prot_o': [3],  # niveaux: [3]

            # Les installations Ex "o" - INSTALLATION – SYSTÈMES DE CHAUFFAGE
            'reglage_coupure_o': [],  # niveaux: []
            'reinitialisation_coupure_outil': [],  # niveaux: []
            'reinitialisation_auto_o': [],  # niveaux: []
            'capteurs_temperature_o': [3],  # niveaux: [3]
            'dispositifs_coupure_o': [3],  # niveaux: [3]
            'reinitialisation_defaut_o': [3],  # niveaux: [3]
            'coupure_independante_o': [3],  # niveaux: [3]
            'capteur_niveau_o': [3],  # niveaux: [3]
            'capteur_debit_o': [3],  # niveaux: [3]

            # Les installations Ex "o" - ENVIRONNEMENT
            'protection_materiel_o': [],  # niveaux: []
            'accumulation_poussiere_o': [],  # niveaux: []
            'isolation_electrique_o': [3],  # niveaux: [3]

            # Les installations Ex "p" et "pD" - MATERIEL
            'mat_approprie_p': [],  # niveaux: []
            'groupe_correct_p': [2, 3],  # niveaux: [2,3]
            'classe_temp_p': [2, 3],  # niveaux: [2,3]
            'identification_circuit_p': [],  # niveaux: []
            'enveloppe_verre_p': [],  # niveaux: []
            'modification_non_autorisee_p': [1, 2],  # niveaux: [1,2]
            'identification_circuit_correcte_p': [3],  # niveaux: [3]
            'pas_modif_non_autorisee_p': [3],  # niveaux: [3]
            'type_lampes_p': [3],  # niveaux: [3]

            # Les installations Ex "p" et "pD" - INSTALLATION
            'dommage_cables_p': [],  # niveaux: []
            'liaisons_terre_p': [],  # niveaux: []
            'conduites_tubes_p': [],  # niveaux: []
            'gaz_impuretes_p': [],  # niveaux: []
            'pression_debit_gaz': [],  # niveaux: []
            'type_cable_p': [3],  # niveaux: [3]
            'impedance_boucle_it': [3],  # niveaux: [3]
            'protection_electrique_p': [3],  # niveaux: [3]
            'protection_reglee_p': [3],  # niveaux: [3]
            'temp_entree_gaz': [3],  # niveaux: [3]
            'barrieres_etincelles': [3],  # niveaux: [3]
            'conditions_utilisation_p': [3],  # niveaux: [3]

            # Les installations Ex "p" et "pD" - ENVIRONNEMENT
            'protection_materiel_p': [],  # niveaux: []
            'accumulation_poussiere_p': [],  # niveaux: []
        }

        # Helper function to check if a point applies to the selected mode
        def point_applies_to_mode(point_text, mode_protection):
            if not mode_protection:
                return False

            # Look for mode indicators in parentheses like (d), (e), (n), etc.
            import re
            mode_pattern = r'\(([^)]*)\)'
            matches = re.findall(mode_pattern, point_text)

            if not matches:
                # If no mode indicators found, show the point (general points)
                return True

            # Check if any match contains our mode
            for match in matches:
                modes_in_parens = match.lower()
                # Split by common separators and check each mode
                modes = re.split(r'[,/\s]+', modes_in_parens)
                modes = [m.strip() for m in modes if m.strip()]

                current_mode = mode_protection.lower()

                # Check for exact match
                if current_mode in modes:
                    return True

                # Handle tD case (can be written as "td" or "t/td")
                if current_mode == 'td' and ('td' in modes or any('t/td' in m for m in modes)):
                    return True

                # Handle t case when t/tD is present
                if current_mode == 't' and any('t/td' in m for m in modes):
                    return True

            return False

        # Get selected niveaux from inspection
        selected_niveaux = []
        if hasattr(inspection, 'niveau_1') and inspection.niveau_1:
            selected_niveaux.append(1)
        if hasattr(inspection, 'niveau_2') and inspection.niveau_2:
            selected_niveaux.append(2)
        if hasattr(inspection, 'niveau_3') and inspection.niveau_3:
            selected_niveaux.append(3)

        # Define the complete sections structure from frontend
        sections_structure = [
            {
                'title': 'Les installations Ex "d", Ex "e", Ex "n" et Ex "t/tD" (Tableau 01 l’IEC 60079-17V2023)',
                'modes': ['d', 'e', 'n', 't', 'tD'],
                'subsections': [
                    {
                        'subtitle': 'GÉNÉRALITÉS (TOUS LES MATÉRIELS)',
                        'points': [
                            {'key': 'mat_approprié', 'niveaux': []},
                            {'key': 'groupe_correct', 'niveaux': [2,3]},
                            {'key': 'classe_temp', 'niveaux': [2,3]},
                            {'key': 'temp_surface', 'niveaux': [2,3]},
                            {'key': 'degre_protection', 'niveaux': []},
                            {'key': 'identification_circuit', 'niveaux': []},
                            {'key': 'enveloppe_verre', 'niveaux': []},
                            {'key': 'modification_non_autorisee', 'niveaux': [1,2]},
                            {'key': 'boulons_entrees', 'niveaux': []},
                            {'key': 'couvercles_filetes', 'niveaux': []},
                            {'key': 'dispositifs_respiration', 'niveaux': [2,3]},
                            {'key': 'identification_correcte', 'niveaux': [3]},
                            {'key': 'surfaces_joints', 'niveaux': [3]},
                            {'key': 'etat_garnitures', 'niveaux': [3]},
                            {'key': 'penetration_eau', 'niveaux': [3]},
                            {'key': 'dimensions_espaces', 'niveaux': [3]},
                            {'key': 'connexions_serrees', 'niveaux': [3]},
                            {'key': 'bornes_non_utilisees', 'niveaux': [3]},
                            {'key': 'dispositifs_scellement', 'niveaux': [3]},
                            {'key': 'composants_encapsules', 'niveaux': [3]},
                            {'key': 'composants_ignifuges', 'niveaux': [3]},
                            {'key': 'enveloppes_respiration', 'niveaux': [3]},
                            {'key': 'port_essai', 'niveaux': [3]},
                            {'key': 'operation_respiration', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'MATÉRIEL PARTICULIER (ÉCLAIRAGE)',
                        'points': [
                            {'key': 'lampes_fluo', 'niveaux': []},
                            {'key': 'lampes_dhi', 'niveaux': []},
                            {'key': 'type_lampes', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'MATÉRIEL PARTICULIER (MOTEURS)',
                        'points': [
                            {'key': 'distance_ventilateur', 'niveaux': []},
                            {'key': 'circulation_air', 'niveaux': [3]},
                            {'key': 'resistance_isolement', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION - GÉNÉRALITÉS',
                        'points': [
                            {'key': 'dommage_cables', 'niveaux': []},
                            {'key': 'obturation_traversees', 'niveaux': []},
                            {'key': 'liaisons_terre', 'niveaux': []},
                            {'key': 'engorgements_joints', 'niveaux': []},
                            {'key': 'installation_variable', 'niveaux': []},
                            {'key': 'type_cable', 'niveaux': [2]},
                            {'key': 'boitiers_arret', 'niveaux': [3]},
                            {'key': 'integrite_conduits', 'niveaux': [3]},
                            {'key': 'impedance_boucle', 'niveaux': [3]},
                            {'key': 'protection_electrique', 'niveaux': [3]},
                            {'key': 'protection_electrique_chauffage', 'niveaux': [3]},
                            {'key': 'conditions_utilisation', 'niveaux': [3]},
                            {'key': 'extremites_cables', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION - SYSTÈMES DE CHAUFFAGE',
                        'points': [
                            {'key': 'reglage_coupure', 'niveaux': [2,3]},
                            {'key': 'reinitialisation_coupure', 'niveaux': [2,3]},
                            {'key': 'reinitialisation_automatique', 'niveaux': [2,3]},
                            {'key': 'capteurs_temperature', 'niveaux': [3]},
                            {'key': 'dispositifs_coupure', 'niveaux': [3]},
                            {'key': 'reinitialisation_defaut', 'niveaux': [3]},
                            {'key': 'coupure_independante', 'niveaux': [3]},
                            {'key': 'capteur_niveau', 'niveaux': [3]},
                            {'key': 'capteur_debit', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'MOTEURS',
                        'points': [
                            {'key': 'protection_moteur', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'ENVIRONNEMENT',
                        'points': [
                            {'key': 'protection_materiel', 'niveaux': []},
                            {'key': 'accumulation_poussiere', 'niveaux': []},
                            {'key': 'isolation_electrique', 'niveaux': [3]},
                        ]
                    }
                ]
            },
            {
                'title': 'Les installations Ex "i" (Tableau 02 l’IEC 60079-17V2023)',
                'modes': ['i'],
                'subsections': [
                    {
                        'subtitle': 'MATERIEL',
                        'points': [
                            {'key': 'doc_materiel_appropriate', 'niveaux': []},
                            {'key': 'materiel_specifie', 'niveaux': [2,3]},
                            {'key': 'categorie_groupe_correct', 'niveaux': [2,3]},
                            {'key': 'degre_ip_groupe3', 'niveaux': [2,3]},
                            {'key': 'classe_temp_materiel', 'niveaux': [2,3]},
                            {'key': 'plage_temp_ambiante', 'niveaux': [2,3]},
                            {'key': 'plage_temp_service', 'niveaux': [2,3]},
                            {'key': 'installation_reperee', 'niveaux': [2,3]},
                            {'key': 'presse_etoupes_type', 'niveaux': []},
                            {'key': 'modification_non_autorisee_i', 'niveaux': [1,2]},
                            {'key': 'barrieres_securite', 'niveaux': []},
                            {'key': 'tension_max_um', 'niveaux': [2,3]},
                            {'key': 'enveloppe_verre_i', 'niveaux': [3]},
                            {'key': 'pas_modif_non_autorisee', 'niveaux': [3]},
                            {'key': 'etat_garnitures_env', 'niveaux': [3]},
                            {'key': 'connexions_serrees_i', 'niveaux': [3]},
                            {'key': 'cartes_propres', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION',
                        'points': [
                            {'key': 'dommage_cables_i', 'niveaux': []},
                            {'key': 'obturation_traversees_i', 'niveaux': []},
                            {'key': 'cables_documentation', 'niveaux': [3]},
                            {'key': 'ecrans_documentation', 'niveaux': [3]},
                            {'key': 'connexions_point_point', 'niveaux': [3]},
                            {'key': 'continuite_terre', 'niveaux': [3]},
                            {'key': 'liaison_terre_integrite', 'niveaux': [3]},
                            {'key': 'mise_a_la_terre_sic', 'niveaux': [3]},
                            {'key': 'resistance_isolement_i', 'niveaux': [3]},
                            {'key': 'separation_circuits', 'niveaux': [3]},
                            {'key': 'protection_cc_alim', 'niveaux': [3]},
                            {'key': 'conditions_utilisation_i', 'niveaux': [3]},
                            {'key': 'extremites_cables_prot', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'ENVIRONNEMENT',
                        'points': [
                            {'key': 'protection_materiel_i', 'niveaux': []},
                            {'key': 'accumulation_poussiere_ext', 'niveaux': []},
                        ]
                    }
                ]
            },
            {
                'title': 'Les installations Ex "o" (Tableau 04 l’IEC 60079-17V2023)',
                'modes': ['o'],
                'subsections': [
                    {
                        'subtitle': 'ÉQUIPMENT',
                        'points': [
                            {'key': 'mat_approprie_o', 'niveaux': []},
                            {'key': 'groupe_correct_o', 'niveaux': [2,3]},
                            {'key': 'classe_temp_o', 'niveaux': [2,3]},
                            {'key': 'identification_circuit_o', 'niveaux': []},
                            {'key': 'enveloppe_verre_o', 'niveaux': []},
                            {'key': 'modification_non_autorisee_o', 'niveaux': [1,2]},
                            {'key': 'boulons_entrees_o', 'niveaux': []},
                            {'key': 'enveloppes_scellees_o', 'niveaux': []},
                            {'key': 'critere_liquide_protection', 'niveaux': []},
                            {'key': 'fonctionnement_tele_signalisation', 'niveaux': []},
                            {'key': 'programme_nettoyage_liquide', 'niveaux': []},
                            {'key': 'identification_circuits_o', 'niveaux': [3]},
                            {'key': 'pas_modif_non_autorisee_o', 'niveaux': [3]},
                            {'key': 'connexions_serrees_o', 'niveaux': [3]},
                            {'key': 'etat_joints_etancheite', 'niveaux': [3]},
                            {'key': 'dispositifs_respiration_o', 'niveaux': [3]},
                            {'key': 'surpression_enveloppe', 'niveaux': [3]},
                            {'key': 'niveau_liquide_o', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION - GÉNÉRALITÉS',
                        'points': [
                            {'key': 'dommage_cables_o', 'niveaux': []},
                            {'key': 'obturation_traversees_o', 'niveaux': []},
                            {'key': 'liaisons_terre_o', 'niveaux': []},
                            {'key': 'installation_variable_o', 'niveaux': []},
                            {'key': 'type_cable_o', 'niveaux': [3]},
                            {'key': 'boitiers_coupe_feu', 'niveaux': [3]},
                            {'key': 'integrite_conduits_o', 'niveaux': [3]},
                            {'key': 'impedance_boucle_o', 'niveaux': [3]},
                            {'key': 'protection_electrique_o', 'niveaux': [3]},
                            {'key': 'protection_electrique_autorisee', 'niveaux': [3]},
                            {'key': 'conditions_utilisation_o', 'niveaux': [3]},
                            {'key': 'extremites_cables_prot_o', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION – SYSTÈMES DE CHAUFFAGE',
                        'points': [
                            {'key': 'reglage_coupure_o', 'niveaux': []},
                            {'key': 'reinitialisation_coupure_outil', 'niveaux': []},
                            {'key': 'reinitialisation_auto_o', 'niveaux': []},
                            {'key': 'capteurs_temperature_o', 'niveaux': [3]},
                            {'key': 'dispositifs_coupure_o', 'niveaux': [3]},
                            {'key': 'reinitialisation_defaut_o', 'niveaux': [3]},
                            {'key': 'coupure_independante_o', 'niveaux': [3]},
                            {'key': 'capteur_niveau_o', 'niveaux': [3]},
                            {'key': 'capteur_debit_o', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'ENVIRONNEMENT',
                        'points': [
                            {'key': 'protection_materiel_o', 'niveaux': []},
                            {'key': 'accumulation_poussiere_o', 'niveaux': []},
                            {'key': 'isolation_electrique_o', 'niveaux': [3]},
                        ]
                    }
                ]
            },
            {
                'title': 'Les installations Ex "p" et "pD" (Tableau 03 l’IEC 60079-17V2023)',
                'modes': ['p', 'pD'],
                'subsections': [
                    {
                        'subtitle': 'MATERIEL',
                        'points': [
                            {'key': 'mat_approprie_p', 'niveaux': []},
                            {'key': 'groupe_correct_p', 'niveaux': [2,3]},
                            {'key': 'classe_temp_p', 'niveaux': [2,3]},
                            {'key': 'identification_circuit_p', 'niveaux': []},
                            {'key': 'enveloppe_verre_p', 'niveaux': []},
                            {'key': 'modification_non_autorisee_p', 'niveaux': [1,2]},
                            {'key': 'identification_circuit_correcte_p', 'niveaux': [3]},
                            {'key': 'pas_modif_non_autorisee_p', 'niveaux': [3]},
                            {'key': 'type_lampes_p', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'INSTALLATION',
                        'points': [
                            {'key': 'dommage_cables_p', 'niveaux': []},
                            {'key': 'liaisons_terre_p', 'niveaux': []},
                            {'key': 'conduites_tubes_p', 'niveaux': []},
                            {'key': 'gaz_impuretes_p', 'niveaux': []},
                            {'key': 'pression_debit_gaz', 'niveaux': []},
                            {'key': 'type_cable_p', 'niveaux': [3]},
                            {'key': 'impedance_boucle_it', 'niveaux': [3]},
                            {'key': 'protection_electrique_p', 'niveaux': [3]},
                            {'key': 'protection_reglee_p', 'niveaux': [3]},
                            {'key': 'temp_entree_gaz', 'niveaux': [3]},
                            {'key': 'barrieres_etincelles', 'niveaux': [3]},
                            {'key': 'conditions_utilisation_p', 'niveaux': [3]},
                        ]
                    },
                    {
                        'subtitle': 'ENVIRONNEMENT',
                        'points': [
                            {'key': 'protection_materiel_p', 'niveaux': []},
                            {'key': 'accumulation_poussiere_p', 'niveaux': []},
                        ]
                    }
                ]
            }
        ]

        # Process sections with filtering
        print(f"DEBUG: Processing sections with mode: {inspection.mode_protection}, niveaux: {selected_niveaux}")

        # Check if any sections should be displayed
        visible_sections = []
        mode_protection = getattr(inspection, 'mode_protection', '') or ''
        for section in sections_structure:
            if mode_protection in section['modes']:
                visible_sections.append(section)

        if not visible_sections:
            print("DEBUG: No sections match the selected mode de protection")
            # Add a message indicating no sections available
            points_data.append([
                Paragraph("Aucune section d'inspection disponible pour le mode de protection sélectionné", points_style),
                '', '', ''
            ])
        else:
            print(f"DEBUG: Found {len(visible_sections)} matching sections")

            for section in visible_sections:
                # Add section title
                section_title = Paragraph(f'<b>{section["title"]}</b>', section_header_style)
                points_data.append([section_title, '', '', ''])

                for subsection in section['subsections']:
                    # Check if subsection should be shown based on equipment type
                    if subsection['subtitle'] == 'MATÉRIEL PARTICULIER (MOTEURS)':
                        # Only show MOTEURS section for specific equipment types
                        equipment_type = getattr(inspection, 'equipement', '') or ''
                        equipment_custom = getattr(inspection, 'equipement_custom', '') or ''

                        # Check if equipment is motor or pump related
                        motor_keywords = ['moteur', 'pompe', 'motor', 'pump']
                        is_motor_equipment = False

                        # Check main equipment field
                        if any(keyword.lower() in equipment_type.lower() for keyword in motor_keywords):
                            is_motor_equipment = True

                        # Check custom equipment field if equipment is 'custom'
                        if equipment_type.lower() == 'custom' and equipment_custom:
                            if any(keyword.lower() in equipment_custom.lower() for keyword in motor_keywords):
                                is_motor_equipment = True

                        # Skip this subsection if not motor/pump equipment
                        if not is_motor_equipment:
                            print(f"DEBUG: Skipping MOTEURS section - equipment: {equipment_type}, custom: {equipment_custom}")
                            continue
                        else:
                            print(f"DEBUG: Showing MOTEURS section - equipment: {equipment_type}, custom: {equipment_custom}")

                    elif subsection['subtitle'] == 'MATÉRIEL PARTICULIER (ÉCLAIRAGE)':
                        # Only show ÉCLAIRAGE section for luminaire equipment
                        equipment_type = getattr(inspection, 'equipement', '') or ''
                        equipment_custom = getattr(inspection, 'equipement_custom', '') or ''

                        # Check if equipment is luminaire related
                        luminaire_keywords = ['luminaire', 'éclairage', 'eclairage', 'lighting', 'lamp', 'lampe']
                        is_luminaire_equipment = False

                        # Check main equipment field
                        if any(keyword.lower() in equipment_type.lower() for keyword in luminaire_keywords):
                            is_luminaire_equipment = True

                        # Check custom equipment field if equipment is 'custom'
                        if equipment_type.lower() == 'custom' and equipment_custom:
                            if any(keyword.lower() in equipment_custom.lower() for keyword in luminaire_keywords):
                                is_luminaire_equipment = True

                        # Skip this subsection if not luminaire equipment
                        if not is_luminaire_equipment:
                            print(f"DEBUG: Skipping ÉCLAIRAGE section - equipment: {equipment_type}, custom: {equipment_custom}")
                            continue
                        else:
                            print(f"DEBUG: Showing ÉCLAIRAGE section - equipment: {equipment_type}, custom: {equipment_custom}")

                    # Check if subsection has any visible points
                    visible_points = []
                    for point in subsection['points']:
                        point_key = point['key']
                        point_niveaux = point['niveaux']

                        # Apply niveau filtering
                        if point_niveaux:  # If point has niveau requirements
                            if not any(niveau in selected_niveaux for niveau in point_niveaux):
                                continue
                        else:
                            # If no niveau requirements, show for all levels (but only if at least one niveau is selected)
                            if not selected_niveaux:
                                continue

                        # Check if point exists in inspection data (with safe access)
                        if hasattr(inspection, 'points') and inspection.points and point_key in inspection.points:
                            visible_points.append(point)

                    # Only show subsection if it has visible points
                    if visible_points:
                        # Add subsection title
                        subsection_title = Paragraph(f'<i>{subsection["subtitle"]}</i>', normal_style)
                        points_data.append([subsection_title, '', '', ''])

                        # Add points
                        for point in visible_points:
                            point_key = point['key']
                            point_data = inspection.points.get(point_key, {}) if inspection.points else {}

                            point_text = point_labels.get(point_key, point_key.replace('_', ' ').title())
                            is_correct = point_data.get('correct', False)
                            is_defaut = point_data.get('defaut', False)
                            # Safely extract F, G, C values with fallbacks
                            try:
                                f_value = point_data.get('f', '') if isinstance(point_data, dict) else ''
                                g_value = point_data.get('g', '') if isinstance(point_data, dict) else ''
                                c_value_raw = point_data.get('c', '') if isinstance(point_data, dict) else ''

                                # Create colored box for C column if color is specified
                                if c_value_raw and c_value_raw.startswith('#'):
                                    try:
                                        # Convert hex color to RGB
                                        hex_color = c_value_raw.lstrip('#')
                                        if len(hex_color) == 6:
                                            r = int(hex_color[0:2], 16) / 255.0
                                            g = int(hex_color[2:4], 16) / 255.0
                                            b = int(hex_color[4:6], 16) / 255.0

                                            # Create a colored box using Paragraph with background color
                                            from reportlab.lib.colors import Color
                                            color_obj = Color(r, g, b)
                                            c_value = Paragraph('■', ParagraphStyle(
                                                'ColorBox',
                                                parent=points_style,
                                                fontSize=16,
                                                textColor=color_obj,
                                                alignment=TA_CENTER
                                            ))
                                        else:
                                            c_value = ''
                                    except Exception as color_error:
                                        print(f"DEBUG: Error creating color box for {c_value_raw}: {color_error}")
                                        c_value = ''
                                else:
                                    c_value = ''

                            except Exception as e:
                                print(f"DEBUG: Error extracting F,G,C values for point {point_key}: {e}")
                                f_value = g_value = c_value = ''
                            remarques = point_data.get('remarques', '')

                            # Create Paragraph for better text wrapping
                            point_paragraph = Paragraph(point_text, points_style)

                            # Create clean checkbox representations
                            correct_check = '✓' if is_correct else ''
                            defaut_check = '✓' if is_defaut else ''

                            # Create Paragraph for remarks
                            remarques_paragraph = Paragraph(remarques or '', points_style)

                            points_data.append([point_paragraph, correct_check, defaut_check, f_value, g_value, c_value, remarques_paragraph])

        print(f"DEBUG: Final points_data has {len(points_data)} rows")

        # Combine header and data
        points_table_data = points_header + points_data

        # Adjust column widths to better accommodate long text and new columns
        points_table = Table(points_table_data, colWidths=[7*cm, 1.5*cm, 1.5*cm, 1.5*cm, 1.5*cm, 1.5*cm, 3.5*cm])

        # Create dynamic table style based on content
        table_style = [
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),  # Header row
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # Header row bold
        ]

        # Add styling for section titles, subtitles, and points
        for i, row in enumerate(points_table_data):
            if i == 0:  # Skip header row
                continue

            # Check if this is a section title (contains "Les installations Ex")
            if row[0] and hasattr(row[0], 'text') and 'Les installations Ex' in str(row[0].text):
                table_style.extend([
                    ('SPAN', (0, i), (-1, i)),  # Span across all columns
                    ('BACKGROUND', (0, i), (-1, i), colors.darkgrey),
                    ('FONTSIZE', (0, i), (-1, i), 11),
                    ('FONTNAME', (0, i), (-1, i), 'Helvetica-Bold'),
                    ('ALIGN', (0, i), (-1, i), 'CENTER'),
                ])
            # Check if this is a subsection title (italic text)
            elif row[0] and hasattr(row[0], 'text') and '<i>' in str(row[0].text):
                table_style.extend([
                    ('SPAN', (0, i), (-1, i)),  # Span across all columns
                    ('BACKGROUND', (0, i), (-1, i), colors.lightblue),
                    ('FONTSIZE', (0, i), (-1, i), 10),
                    ('FONTNAME', (0, i), (-1, i), 'Helvetica-Bold'),
                    ('ALIGN', (0, i), (-1, i), 'LEFT'),
                ])
            # Regular point rows
            else:
                table_style.extend([
                    ('ALIGN', (1, i), (2, i), 'CENTER'),  # Center checkmarks
                    ('FONTNAME', (1, i), (2, i), 'Helvetica-Bold'),  # Make checkmarks bold
                    ('FONTSIZE', (1, i), (2, i), 16),  # Make checkmarks larger
                    ('TEXTCOLOR', (1, i), (2, i), colors.green),  # Make checkmarks green
                ])

        # Apply alternating row colors for point rows only (not titles/subtitles)
        point_row_count = 0
        for i, row in enumerate(points_table_data):
            if i == 0:  # Skip header
                continue
            # Skip section titles and subtitles for alternating colors
            if (row[0] and hasattr(row[0], 'text') and
                ('Les installations Ex' in str(row[0].text) or '<i>' in str(row[0].text))):
                continue
            else:
                # Apply alternating background for point rows
                bg_color = colors.white if point_row_count % 2 == 0 else colors.lightgrey
                table_style.append(('BACKGROUND', (0, i), (-1, i), bg_color))
                point_row_count += 1

        points_table.setStyle(TableStyle(table_style))
        elements.append(points_table)
        elements.append(Spacer(1, 0.2*cm))

        # Photos de l'équipement section - only add if photos exist
        try:
            photos_header = [
                [Paragraph('<b>Photos de l\'équipement</b>', section_header_style)]
            ]

            photos_content = []

            # Photo du marquage
            if hasattr(inspection, 'photo_marquage') and inspection.photo_marquage:
                try:
                    from reportlab.platypus import Image as ReportLabImage
                    # Create image with proper sizing
                    photo_path = inspection.photo_marquage.path if hasattr(inspection.photo_marquage, 'path') else str(inspection.photo_marquage)
                    img = ReportLabImage(photo_path, width=6*cm, height=4*cm)
                    photos_content.append([
                        Paragraph('<b>Une photo du marquage</b>', normal_style),
                        img
                    ])
                    photos_content.append([
                        Paragraph('Photo claire du marquage ATEX/certification de l\'équipement', points_style),
                        ''
                    ])
                except Exception as e:
                    print(f"DEBUG: Error loading photo_marquage: {e}")
                    photos_content.append([
                        Paragraph('<b>Une photo du marquage</b>', normal_style),
                        Paragraph('Photo non disponible', points_style)
                    ])
            else:
                photos_content.append([
                    Paragraph('<b>Une photo du marquage</b>', normal_style),
                    Paragraph('Aucune photo fournie', points_style)
                ])

            # Photo de l'équipement
            if hasattr(inspection, 'photo_equipement') and inspection.photo_equipement:
                try:
                    from reportlab.platypus import Image as ReportLabImage
                    photo_path = inspection.photo_equipement.path if hasattr(inspection.photo_equipement, 'path') else str(inspection.photo_equipement)
                    img = ReportLabImage(photo_path, width=6*cm, height=4*cm)
                    photos_content.append([
                        Paragraph('<b>Une photo de l\'équipement</b>', normal_style),
                        img
                    ])
                    photos_content.append([
                        Paragraph('Photo générale de l\'équipement dans son environnement', points_style),
                        ''
                    ])
                except Exception as e:
                    print(f"DEBUG: Error loading photo_equipement: {e}")
                    photos_content.append([
                        Paragraph('<b>Une photo de l\'équipement</b>', normal_style),
                        Paragraph('Photo non disponible', points_style)
                    ])
            else:
                photos_content.append([
                    Paragraph('<b>Une photo de l\'équipement</b>', normal_style),
                    Paragraph('Aucune photo fournie', points_style)
                ])

            # Only add photos section if we have content
            if photos_content:
                # Combine photos header and content
                photos_table_data = photos_header + photos_content
                photos_table = Table(photos_table_data, colWidths=[9*cm, 9*cm])
                photos_table.setStyle(TableStyle([
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('SPAN', (0, 0), (-1, 0)),  # Span header across all columns
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 6),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
                    ('ALIGN', (1, 1), (1, -1), 'CENTER'),  # Center images
                ]))
                elements.append(photos_table)
                elements.append(Spacer(1, 0.2*cm))

        except Exception as e:
            print(f"DEBUG: Error in photos section: {e}")
            # Add a simple message if photos section fails
            photos_fallback = [
                [Paragraph('<b>Photos de l\'équipement</b>', section_header_style)],
                [Paragraph('Section photos temporairement indisponible', normal_style)]
            ]
            photos_table = Table(photos_fallback, colWidths=[18*cm])
            photos_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ]))
            elements.append(photos_table)
            elements.append(Spacer(1, 0.2*cm))

        # Les anomalies section - separate section for anomaly photos
        try:
            anomalies_header = [
                [Paragraph('<b>Les anomalies</b>', section_header_style)]
            ]

            anomalies_content = []

            if hasattr(inspection, 'photos_anomalies') and inspection.photos_anomalies and len(inspection.photos_anomalies) > 0:
                try:
                    # Display up to 4 anomaly photos in a grid
                    anomaly_photos = inspection.photos_anomalies[:4]  # Limit to 4 photos
                    for i, photo_path in enumerate(anomaly_photos):
                        try:
                            from reportlab.platypus import Image as ReportLabImage
                            img = ReportLabImage(photo_path, width=4*cm, height=3*cm)
                            anomalies_content.append([
                                Paragraph(f'Anomalie {i+1}', points_style),
                                img
                            ])
                        except Exception as e:
                            print(f"DEBUG: Error loading anomaly photo {i+1}: {e}")
                            anomalies_content.append([
                                Paragraph(f'Anomalie {i+1}', points_style),
                                Paragraph('Photo non disponible', points_style)
                            ])

                    anomalies_content.append([
                        Paragraph('Photos détaillées de toutes les anomalies détectées lors de l\'inspection', points_style),
                        ''
                    ])
                except Exception as e:
                    print(f"DEBUG: Error processing anomaly photos: {e}")
                    anomalies_content.append([
                        Paragraph('Photos des anomalies non disponibles', points_style),
                        ''
                    ])
            else:
                anomalies_content.append([
                    Paragraph('Aucune photo d\'anomalie fournie', points_style),
                    ''
                ])

            # Combine anomalies header and content
            anomalies_table_data = anomalies_header + anomalies_content
            anomalies_table = Table(anomalies_table_data, colWidths=[9*cm, 9*cm])
            anomalies_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('SPAN', (0, 0), (-1, 0)),  # Span header across all columns
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
                ('ALIGN', (1, 1), (1, -1), 'CENTER'),  # Center images
            ]))
            elements.append(anomalies_table)
            elements.append(Spacer(1, 0.2*cm))

        except Exception as e:
            print(f"DEBUG: Error in anomalies section: {e}")

        # Observations complémentaires section
        observations_header = [
            [Paragraph('<b>Observations complémentaires</b>', section_header_style)]
        ]

        observations_content = [
            [getattr(inspection, 'observations_complementaires', '') or ""]
        ]

        observations_table_data = observations_header + observations_content
        observations_table = Table(observations_table_data, colWidths=[18*cm])
        observations_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
        ]))
        elements.append(observations_table)
        elements.append(Spacer(1, 0.2*cm))

        # Action / Recommendation section
        action_header = [
            [Paragraph('<b>Action / Recommandation</b>', normal_style)]
        ]

        action_content = [
            [getattr(inspection, 'action', '') or ""]
        ]

        action_table_data = action_header + action_content
        action_table = Table(action_table_data, colWidths=[18*cm])
        action_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white]),
        ]))
        elements.append(action_table)

        # Build the PDF
        print("DEBUG: Building PDF document")
        try:
            doc.build(elements)
            print("DEBUG: PDF document built successfully")
        except Exception as e:
            print(f"ERROR: Failed to build PDF document: {str(e)}")
            import traceback
            print(f"ERROR: PDF build traceback: {traceback.format_exc()}")
            from django.http import HttpResponse
            return HttpResponse(f"PDF build error: {str(e)}", status=500, content_type='text/plain')

        # Get the value of the BytesIO buffer
        try:
            pdf = buffer.getvalue()
            buffer.close()
            print(f"DEBUG: PDF generated, size: {len(pdf)} bytes")
        except Exception as e:
            print(f"ERROR: Failed to get PDF buffer: {str(e)}")
            from django.http import HttpResponse
            return HttpResponse(f"PDF buffer error: {str(e)}", status=500, content_type='text/plain')

        # Create the response with the PDF file
        try:
            response = Response(pdf, content_type='application/pdf')
            # Safe filename generation - handle date formatting
            date_str = str(inspection.date).replace('-', '_') if inspection.date else 'no_date'
            fiche_str = str(inspection.fiche_num).replace('/', '_').replace('\\', '_') if inspection.fiche_num else 'no_fiche'
            response['Content-Disposition'] = f'attachment; filename="inspection_{fiche_str}_{date_str}.pdf"'
            print("DEBUG: PDF response created successfully")
            return response
        except Exception as e:
            print(f"ERROR: Failed to create PDF response: {str(e)}")
            from django.http import HttpResponse
            return HttpResponse(f"PDF response error: {str(e)}", status=500, content_type='text/plain')


class CompanyLogoAPIView(APIView):
    """API view for managing company-specific logos"""
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get logos for a specific company or all companies"""
        try:
            company_name = request.query_params.get('company_name')
            logo_type = request.query_params.get('logo_type')

            queryset = CompanyLogo.objects.filter(user=request.user)

            if company_name:
                queryset = queryset.filter(company_name=company_name)
            if logo_type:
                queryset = queryset.filter(logo_type=logo_type)

            logos = []
            for logo in queryset:
                logos.append({
                    'id': logo.id,
                    'logo_type': logo.logo_type,
                    'company_name': logo.company_name,
                    'logo_url': logo.logo.url if logo.logo else None,
                    'uploaded_at': logo.uploaded_at,
                    'updated_at': logo.updated_at
                })

            return Response({'logos': logos})
        except Exception as e:
            return Response(
                {'error': f'Error fetching logos: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """Upload or update a company-specific logo"""
        try:
            logo_type = request.data.get('logo_type')
            company_name = request.data.get('company_name')
            logo_file = request.FILES.get('logo')

            if not logo_type or logo_type not in ['performing_company', 'inspected_company']:
                return Response(
                    {'error': 'Invalid logo_type. Must be performing_company or inspected_company'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not company_name:
                return Response(
                    {'error': 'Company name is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not logo_file:
                return Response(
                    {'error': 'No logo file provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if logo_file.content_type not in allowed_types:
                return Response(
                    {'error': 'Invalid file type. Only JPG, PNG, and GIF are allowed'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate file size (5MB max)
            if logo_file.size > 5 * 1024 * 1024:
                return Response(
                    {'error': 'File too large. Maximum size is 5MB'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create or update the logo for the specific company
            logo_obj, created = CompanyLogo.objects.update_or_create(
                user=request.user,
                logo_type=logo_type,
                company_name=company_name,
                defaults={'logo': logo_file}
            )

            return Response({
                'id': logo_obj.id,
                'logo_type': logo_obj.logo_type,
                'company_name': logo_obj.company_name,
                'logo_url': logo_obj.logo.url,
                'message': f'Logo {"created" if created else "updated"} successfully'
            })

        except Exception as e:
            return Response(
                {'error': f'Error uploading logo: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def delete(self, request):
        """Delete a company-specific logo"""
        try:
            logo_id = request.data.get('logo_id')
            logo_type = request.data.get('logo_type')
            company_name = request.data.get('company_name')

            if logo_id:
                # Delete by ID
                logo_obj = CompanyLogo.objects.filter(
                    user=request.user,
                    id=logo_id
                ).first()
            elif logo_type and company_name:
                # Delete by logo_type and company_name
                logo_obj = CompanyLogo.objects.filter(
                    user=request.user,
                    logo_type=logo_type,
                    company_name=company_name
                ).first()
            else:
                return Response(
                    {'error': 'Either logo_id or both logo_type and company_name are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not logo_obj:
                return Response(
                    {'error': 'Logo not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Delete the file from storage
            if logo_obj.logo:
                logo_obj.logo.delete()

            # Delete the database record
            logo_obj.delete()

            return Response({
                'message': f'Logo for {logo_obj.company_name} deleted successfully'
            })

        except Exception as e:
            return Response(
                {'error': f'Error deleting logo: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class MediaTestAPIView(APIView):
    """Test view to check media file serving"""
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Test media file access"""
        try:
            # Get all user files for the user
            user_files = UserFile.objects.filter(user=request.user)

            test_results = []
            for file_obj in user_files:
                if file_obj.file:
                    import os
                    file_exists = os.path.exists(file_obj.file.path)
                    # Test if the secure media URL works
                    secure_url = f"http://197.140.142.170/media/{file_obj.file.name}"

                    test_results.append({
                        'id': file_obj.id,
                        'name': file_obj.name,
                        'file_path': file_obj.file.path,
                        'file_url': file_obj.file.url,
                        'secure_url': secure_url,
                        'file_exists': file_exists,
                        'file_size': os.path.getsize(file_obj.file.path) if file_exists else 0,
                        'folder_id': file_obj.folder_id
                    })

            return Response({
                'media_root': settings.MEDIA_ROOT,
                'media_url': settings.MEDIA_URL,
                'debug': settings.DEBUG,
                'user_files': test_results,
                'test_info': {
                    'user': request.user.username,
                    'token_exists': bool(request.auth),
                    'total_files': len(test_results)
                }
            })
        except Exception as e:
            return Response(
                {'error': f'Error testing media: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )