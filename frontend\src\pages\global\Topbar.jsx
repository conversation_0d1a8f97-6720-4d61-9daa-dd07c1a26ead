// Topbar.js

import React from 'react';
import { Box, IconButton } from "@mui/material";
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import { useNavigate } from 'react-router-dom';

const Topbar = () => {
  const navigate = useNavigate();

  const handleSettingsClick = () => {
    navigate('/settingsMain'); // Navigate to the settings page
  };

  return (
    <Box
      display="flex"
      justifyContent="flex-end"
      p={2}
      sx={{
        backgroundColor: '#f9f9fc',
        position: 'fixed',
        top: 0,
        left: '280px', // Adjust this value based on your sidebar width
        right: 0,
        zIndex: 1000,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}
    >
      {/* ICONS */}
      <Box display="flex">
        <IconButton>
          <NotificationsIcon sx={{ color: '#004381', fontSize: '30px' }} /> {/* Set color and size */}
        </IconButton>
        <IconButton onClick={handleSettingsClick}>
          <SettingsIcon sx={{ color: '#004381', fontSize: '30px' }} /> {/* Set color and size */}
        </IconButton>
      </Box>
    </Box>
  );
};

export default Topbar;