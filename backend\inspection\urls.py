from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import InspectionViewSet, UserFileViewSet, CompanyViewSet, CompanyLogoAPIView, MediaTestAPIView

router = DefaultRouter()
router.register(r'inspections', InspectionViewSet, basename='inspection')
router.register(r'userfiles', UserFileViewSet, basename='userfile')
router.register(r'companies', CompanyViewSet, basename='company')

urlpatterns = [
    path('', include(router.urls)),
    path('settings/logos/', CompanyLogoAPIView.as_view(), name='company-logos'),
    path('settings/logos/<str:logo_type>/', CompanyLogoAPIView.as_view(), name='company-logo-detail'),
    path('test/media/', MediaTestAPIView.as_view(), name='media-test'),
]

print(router.urls)
